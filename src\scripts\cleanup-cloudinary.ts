#!/usr/bin/env tsx

/**
 * Simple script to cleanup orphaned Cloudinary files
 * Run this script when you need to clean up files that were deleted from database but not from Cloudinary
 */

import { supabaseAdmin } from '@/lib/supabase';
import { deleteFromCloudinary } from '@/lib/cloudinary';

interface OrphanedFile {
  id: number;
  filename: string;
  cloudinary_public_id: string;
  deleted_at: string | null;
  deleted_by: string | null;
}

async function findOrphanedFiles(): Promise<OrphanedFile[]> {
  if (!supabaseAdmin) {
    throw new Error('Supabase admin client not available');
  }

  console.log('🔍 Finding orphaned files...');

  // Find files that are soft deleted but might still exist in Cloudinary
  const { data: softDeleted, error: softError } = await supabaseAdmin
    .from('media_files')
    .select('id, filename, cloudinary_public_id, deleted_at, deleted_by')
    .not('deleted_at', 'is', null)
    .not('cloudinary_public_id', 'is', null)
    .not('cloudinary_public_id', 'eq', '');

  if (softError) {
    console.error('❌ Error finding soft deleted files:', softError);
    return [];
  }

  console.log(`📋 Found ${softDeleted?.length || 0} soft deleted files with Cloudinary IDs`);
  return softDeleted || [];
}

async function cleanupCloudinaryFile(file: OrphanedFile): Promise<boolean> {
  try {
    console.log(`🧹 Cleaning up: ${file.filename} (${file.cloudinary_public_id})`);
    
    const deleted = await deleteFromCloudinary(file.cloudinary_public_id);
    
    if (deleted) {
      console.log(`✅ Successfully deleted from Cloudinary: ${file.filename}`);
      return true;
    } else {
      console.warn(`⚠️ Failed to delete from Cloudinary: ${file.filename}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error deleting ${file.filename}:`, error);
    return false;
  }
}

async function cleanupOrphanedFiles(dryRun: boolean = true): Promise<void> {
  try {
    console.log('🚀 Starting Cloudinary cleanup...');
    console.log(`📋 Mode: ${dryRun ? 'DRY RUN (no actual deletions)' : 'LIVE (will delete files)'}`);
    
    const orphanedFiles = await findOrphanedFiles();
    
    if (orphanedFiles.length === 0) {
      console.log('✅ No orphaned files found!');
      return;
    }

    console.log(`\n📊 Summary:`);
    console.log(`   • Found ${orphanedFiles.length} orphaned files`);
    console.log(`   • Mode: ${dryRun ? 'DRY RUN' : 'LIVE CLEANUP'}`);
    
    if (dryRun) {
      console.log('\n📋 Files that would be deleted:');
      orphanedFiles.forEach((file, index) => {
        console.log(`   ${index + 1}. ${file.filename} (${file.cloudinary_public_id})`);
      });
      console.log('\n💡 To actually delete these files, run with --live flag');
      return;
    }

    // Confirm before proceeding with live cleanup
    console.log('\n⚠️  WARNING: This will permanently delete files from Cloudinary!');
    console.log('   Press Ctrl+C to cancel, or wait 5 seconds to proceed...');
    
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('\n🧹 Starting cleanup...');
    
    let successful = 0;
    let failed = 0;
    
    for (const file of orphanedFiles) {
      const success = await cleanupCloudinaryFile(file);
      if (success) {
        successful++;
      } else {
        failed++;
      }
      
      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log('\n📊 Cleanup completed:');
    console.log(`   ✅ Successfully deleted: ${successful}`);
    console.log(`   ❌ Failed to delete: ${failed}`);
    console.log(`   📋 Total processed: ${orphanedFiles.length}`);
    
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    process.exit(1);
  }
}

// Command line interface
async function main() {
  const args = process.argv.slice(2);
  const isLive = args.includes('--live') || args.includes('-l');
  const dryRun = !isLive;
  
  console.log('🧹 Cloudinary Cleanup Tool');
  console.log('==========================\n');
  
  if (dryRun) {
    console.log('🔍 Running in DRY RUN mode (no files will be deleted)');
    console.log('💡 Use --live flag to actually delete files\n');
  } else {
    console.log('⚠️  Running in LIVE mode (files will be permanently deleted)');
    console.log('🚨 Make sure you have backups before proceeding!\n');
  }
  
  await cleanupOrphanedFiles(dryRun);
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { cleanupOrphanedFiles, findOrphanedFiles, cleanupCloudinaryFile };
