'use client';

import React, { useState, useEffect } from 'react';
import { MediaCleanupService, MediaCleanupLog } from '@/lib/services/mediaCleanupService';
import { 
  TrashIcon, 
  ExclamationTriangleIcon, 
  CheckCircleIcon,
  ClockIcon,
  PlayIcon
} from '@heroicons/react/24/outline';

export default function MediaCleanupPage() {
  const [logs, setLogs] = useState<MediaCleanupLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [cleanupRunning, setCleanupRunning] = useState(false);
  const [cleanupResult, setCleanupResult] = useState<any>(null);

  const cleanupService = MediaCleanupService.getInstance();

  useEffect(() => {
    loadLogs();
  }, []);

  const loadLogs = async () => {
    setLoading(true);
    try {
      const data = await cleanupService.getCleanupLogs({ limit: 50 });
      setLogs(data);
    } catch (error) {
      console.error('Error loading cleanup logs:', error);
    } finally {
      setLoading(false);
    }
  };

  const runCleanup = async () => {
    setCleanupRunning(true);
    setCleanupResult(null);
    
    try {
      const result = await cleanupService.cleanupOrphanedFiles();
      setCleanupResult(result);
      await loadLogs(); // Refresh logs
    } catch (error) {
      console.error('Error running cleanup:', error);
      setCleanupResult({
        processed: 0,
        successful: 0,
        failed: 1,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      });
    } finally {
      setCleanupRunning(false);
    }
  };

  const getStatusIcon = (log: MediaCleanupLog) => {
    if (!log.webhook_called) {
      return <ClockIcon className="h-5 w-5 text-yellow-500" title="Pending" />;
    }
    if (log.webhook_status === 200) {
      return <CheckCircleIcon className="h-5 w-5 text-green-500" title="Success" />;
    }
    return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" title="Failed" />;
  };

  const getTriggerBadge = (trigger: string) => {
    const colors = {
      'media_library': 'bg-blue-100 text-blue-800',
      'database_delete': 'bg-purple-100 text-purple-800',
      'cleanup_orphaned': 'bg-orange-100 text-orange-800',
      'manual': 'bg-gray-100 text-gray-800'
    };
    
    return (
      <span className={`px-2 py-1 text-xs rounded-full ${colors[trigger as keyof typeof colors] || colors.manual}`}>
        {trigger.replace('_', ' ')}
      </span>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Media Cleanup Management</h1>
          <p className="mt-2 text-gray-600">
            Monitor and manage Cloudinary cleanup operations triggered by database deletions.
          </p>
        </div>

        {/* Actions */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Cleanup Operations</h2>
              <p className="text-sm text-gray-600">
                Run manual cleanup for orphaned files or monitor automatic cleanup logs.
              </p>
            </div>
            <button
              onClick={runCleanup}
              disabled={cleanupRunning}
              className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 disabled:opacity-50 flex items-center"
            >
              {cleanupRunning ? (
                <ClockIcon className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <PlayIcon className="h-4 w-4 mr-2" />
              )}
              {cleanupRunning ? 'Running Cleanup...' : 'Run Orphaned Cleanup'}
            </button>
          </div>

          {/* Cleanup Result */}
          {cleanupResult && (
            <div className="mt-4 p-4 bg-gray-50 rounded-md">
              <h3 className="font-medium text-gray-900 mb-2">Cleanup Results:</h3>
              <div className="grid grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Processed:</span>
                  <span className="ml-2 font-medium">{cleanupResult.processed}</span>
                </div>
                <div>
                  <span className="text-gray-600">Successful:</span>
                  <span className="ml-2 font-medium text-green-600">{cleanupResult.successful}</span>
                </div>
                <div>
                  <span className="text-gray-600">Failed:</span>
                  <span className="ml-2 font-medium text-red-600">{cleanupResult.failed}</span>
                </div>
                <div>
                  <span className="text-gray-600">Errors:</span>
                  <span className="ml-2 font-medium">{cleanupResult.errors.length}</span>
                </div>
              </div>
              {cleanupResult.errors.length > 0 && (
                <div className="mt-2">
                  <details>
                    <summary className="text-sm text-red-600 cursor-pointer">View Errors</summary>
                    <ul className="mt-2 text-xs text-red-600 space-y-1">
                      {cleanupResult.errors.map((error: string, index: number) => (
                        <li key={index}>• {error}</li>
                      ))}
                    </ul>
                  </details>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Logs Table */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Cleanup Logs</h2>
              <button
                onClick={loadLogs}
                className="text-sm text-orange-600 hover:text-orange-700"
              >
                Refresh
              </button>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    File
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Cloudinary ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Trigger
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Error
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                      Loading...
                    </td>
                  </tr>
                ) : logs.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                      No cleanup logs found
                    </td>
                  </tr>
                ) : (
                  logs.map((log) => (
                    <tr key={log.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusIcon(log)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {log.filename}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {log.file_id}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <code className="bg-gray-100 px-2 py-1 rounded text-xs">
                          {log.cloudinary_public_id}
                        </code>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getTriggerBadge(log.deletion_trigger)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(log.created_at).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                        {log.webhook_error && (
                          <span title={log.webhook_error}>
                            {log.webhook_error.length > 30 
                              ? `${log.webhook_error.substring(0, 30)}...`
                              : log.webhook_error
                            }
                          </span>
                        )}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
