-- ========================================
-- INSTANT CLOUDINARY CLEANUP TRIGGER
-- When database records are deleted, instantly trigger Cloudinary cleanup
-- ========================================

-- Function to instantly cleanup Cloudinary when database records are deleted
CREATE OR REPLACE FUNCTION instant_cloudinary_cleanup()
RETURNS TRIGGER AS $$
DECLARE
    api_url TEXT;
    http_response RECORD;
BEGIN
    -- Only proceed if the deleted record has a Cloudinary public ID
    IF OLD.cloudinary_public_id IS NOT NULL AND OLD.cloudinary_public_id != '' THEN
        
        RAISE NOTICE 'Instant Cloudinary cleanup for: % (ID: %)', OLD.filename, OLD.cloudinary_public_id;
        
        -- Call the hard delete API endpoint directly
        api_url := 'http://localhost:3000/api/media/files/' || OLD.id || '?user_id=system&hard_delete=true';
        
        -- Make HTTP DELETE request to trigger Cloudinary cleanup
        BEGIN
            SELECT INTO http_response * FROM net.http_delete(
                url := api_url,
                headers := '{"Content-Type": "application/json"}'::jsonb
            );
            
            IF http_response.status = 200 THEN
                RAISE NOTICE 'Successfully triggered Cloudinary cleanup for: %', OLD.filename;
            ELSE
                RAISE WARNING 'Cloudinary cleanup API returned status % for file: %', http_response.status, OLD.filename;
            END IF;
                
        EXCEPTION WHEN OTHERS THEN
            RAISE WARNING 'Failed to call Cloudinary cleanup API for %: %', OLD.filename, SQLERRM;
        END;
    END IF;

    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS trigger_instant_cloudinary_cleanup ON media_files;

-- Create the trigger for DELETE operations
-- This fires AFTER DELETE to ensure the record is actually deleted
CREATE TRIGGER trigger_instant_cloudinary_cleanup
    AFTER DELETE ON media_files
    FOR EACH ROW
    EXECUTE FUNCTION instant_cloudinary_cleanup();

-- Enable the pg_net extension if not already enabled
-- This is required for HTTP requests from database
CREATE EXTENSION IF NOT EXISTS pg_net;

-- Grant permissions for HTTP requests
GRANT USAGE ON SCHEMA net TO postgres;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA net TO postgres;

-- Test the trigger (uncomment to test)
/*
-- Insert a test record
INSERT INTO media_files (
    filename, 
    original_filename, 
    file_path, 
    file_url, 
    file_type, 
    mime_type, 
    file_size, 
    file_extension, 
    uploaded_by, 
    cloudinary_public_id
) VALUES (
    'test-trigger.jpg', 
    'test-trigger.jpg', 
    'test/path', 
    'http://test.com/test.jpg', 
    'image', 
    'image/jpeg', 
    1000, 
    'jpg', 
    'system', 
    'test_trigger_public_id'
);

-- Delete the test record (this should trigger Cloudinary cleanup)
DELETE FROM media_files WHERE filename = 'test-trigger.jpg';
*/
