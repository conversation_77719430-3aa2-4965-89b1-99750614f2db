import { NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { deleteFromCloudinary } from '@/lib/cloudinary';

/**
 * Clean up Cloudinary files for records that were deleted from database
 */
export async function POST() {
  try {
    if (!supabaseAdmin) {
      throw new Error('Supabase admin client not available');
    }

    console.log('🧹 Checking for deleted files to cleanup...');

    // Get files that were deleted but not yet cleaned up
    const { data: deletedFiles, error } = await supabaseAdmin
      .from('deleted_media_files_log')
      .select('*')
      .eq('cleaned_up', false)
      .order('deleted_at', { ascending: true })
      .limit(50); // Process in batches

    if (error) {
      throw new Error(`Failed to fetch deleted files: ${error.message}`);
    }

    if (!deletedFiles || deletedFiles.length === 0) {
      console.log('✅ No deleted files to cleanup');
      return NextResponse.json({
        success: true,
        message: 'No deleted files to cleanup',
        processed: 0
      });
    }

    console.log(`📋 Found ${deletedFiles.length} deleted files to cleanup`);

    let successful = 0;
    let failed = 0;

    // Process each deleted file
    for (const file of deletedFiles) {
      try {
        console.log(`🧹 Cleaning up: ${file.filename} (${file.cloudinary_public_id})`);
        
        // Update cleanup attempt timestamp
        await supabaseAdmin
          .from('deleted_media_files_log')
          .update({ cleanup_attempted_at: new Date().toISOString() })
          .eq('id', file.id);
        
        // Delete from Cloudinary
        const deleted = await deleteFromCloudinary(file.cloudinary_public_id);
        
        if (deleted) {
          successful++;
          console.log(`✅ Successfully deleted from Cloudinary: ${file.filename}`);
          
          // Mark as cleaned up
          await supabaseAdmin
            .from('deleted_media_files_log')
            .update({ 
              cleaned_up: true,
              cleanup_error: null
            })
            .eq('id', file.id);
            
        } else {
          failed++;
          const errorMsg = `Failed to delete from Cloudinary`;
          console.warn(`⚠️ ${errorMsg}: ${file.filename}`);
          
          // Log the error
          await supabaseAdmin
            .from('deleted_media_files_log')
            .update({ cleanup_error: errorMsg })
            .eq('id', file.id);
        }
        
        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (fileError) {
        failed++;
        const errorMsg = fileError instanceof Error ? fileError.message : 'Unknown error';
        console.error(`❌ Error cleaning up ${file.filename}:`, fileError);
        
        // Log the error
        await supabaseAdmin
          .from('deleted_media_files_log')
          .update({ cleanup_error: errorMsg })
          .eq('id', file.id);
      }
    }

    const result = {
      success: true,
      message: `Cleanup completed: ${successful} successful, ${failed} failed`,
      processed: deletedFiles.length,
      successful,
      failed
    };

    console.log('📊 Cleanup completed:', result);
    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Cleanup failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * GET endpoint to check for pending cleanups
 */
export async function GET() {
  try {
    if (!supabaseAdmin) {
      throw new Error('Supabase admin client not available');
    }

    const { data: pendingFiles, error } = await supabaseAdmin
      .from('deleted_media_files_log')
      .select('*')
      .eq('cleaned_up', false)
      .order('deleted_at', { ascending: true });

    if (error) {
      throw new Error(`Failed to fetch pending files: ${error.message}`);
    }

    return NextResponse.json({
      success: true,
      pending_count: pendingFiles?.length || 0,
      pending_files: pendingFiles || []
    });

  } catch (error) {
    console.error('❌ Failed to check pending cleanups:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Failed to check pending cleanups',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
