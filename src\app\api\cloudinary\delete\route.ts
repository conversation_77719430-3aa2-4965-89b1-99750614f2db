import { NextRequest, NextResponse } from 'next/server';
import { deleteFromCloudinary } from '@/lib/cloudinary';

/**
 * Simple endpoint to delete files from Cloudinary only
 * Used by database triggers when records are deleted
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const publicId = searchParams.get('public_id');
    
    if (!publicId) {
      return NextResponse.json({
        success: false,
        message: 'public_id query parameter is required'
      }, { status: 400 });
    }

    console.log('🧹 Database trigger: Deleting from Cloudinary:', publicId);
    
    // Delete from Cloudinary using existing function
    const deleted = await deleteFromCloudinary(publicId);
    
    if (deleted) {
      console.log('✅ Successfully deleted from Cloudinary:', publicId);
      return NextResponse.json({
        success: true,
        message: 'Successfully deleted from Cloudinary',
        public_id: publicId
      });
    } else {
      console.warn('⚠️ Failed to delete from Cloudinary:', publicId);
      return NextResponse.json({
        success: false,
        message: 'Failed to delete from Cloudinary',
        public_id: publicId
      }, { status: 500 });
    }

  } catch (error) {
    console.error('❌ Cloudinary delete error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Cloudinary delete failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
