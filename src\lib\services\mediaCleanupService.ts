import { supabase, supabaseAdmin } from '@/lib/supabase';
import { deleteFromCloudinary } from '@/lib/cloudinary';

export interface MediaCleanupLog {
  id: number;
  file_id: number;
  filename: string;
  cloudinary_public_id: string;
  deleted_by?: string;
  deletion_trigger: 'media_library' | 'database_delete' | 'manual' | 'cleanup_orphaned';
  webhook_called: boolean;
  webhook_status?: number;
  webhook_error?: string;
  created_at: string;
  updated_at: string;
}

export class MediaCleanupService {
  private static instance: MediaCleanupService;

  static getInstance(): MediaCleanupService {
    if (!MediaCleanupService.instance) {
      MediaCleanupService.instance = new MediaCleanupService();
    }
    return MediaCleanupService.instance;
  }

  /**
   * Log a media deletion event
   */
  async logDeletion(params: {
    file_id: number;
    filename: string;
    cloudinary_public_id: string;
    deleted_by?: string;
    deletion_trigger: MediaCleanupLog['deletion_trigger'];
  }): Promise<boolean> {
    try {
      const client = supabaseAdmin || supabase;

      const { error } = await client
        .from('media_deletion_log')
        .insert({
          file_id: params.file_id,
          filename: params.filename,
          cloudinary_public_id: params.cloudinary_public_id,
          deleted_by: params.deleted_by,
          deletion_trigger: params.deletion_trigger,
          webhook_called: false,
        });

      if (error) {
        console.error('Error logging media deletion:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error logging media deletion:', error);
      return false;
    }
  }

  /**
   * Update webhook status for a deletion log entry
   */
  async updateWebhookStatus(params: {
    file_id: number;
    deletion_trigger: MediaCleanupLog['deletion_trigger'];
    webhook_called: boolean;
    webhook_status?: number;
    webhook_error?: string;
  }): Promise<boolean> {
    try {
      const client = supabaseAdmin || supabase;

      const { error } = await client
        .from('media_deletion_log')
        .update({
          webhook_called: params.webhook_called,
          webhook_status: params.webhook_status,
          webhook_error: params.webhook_error,
          updated_at: new Date().toISOString(),
        })
        .eq('file_id', params.file_id)
        .eq('deletion_trigger', params.deletion_trigger);

      if (error) {
        console.error('Error updating webhook status:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error updating webhook status:', error);
      return false;
    }
  }

  /**
   * Get cleanup logs for monitoring
   */
  async getCleanupLogs(params?: {
    limit?: number;
    deletion_trigger?: MediaCleanupLog['deletion_trigger'];
    webhook_failed_only?: boolean;
  }): Promise<MediaCleanupLog[]> {
    try {
      const client = supabaseAdmin || supabase;
      
      let query = client
        .from('media_deletion_log')
        .select('*')
        .order('created_at', { ascending: false });

      if (params?.limit) {
        query = query.limit(params.limit);
      }

      if (params?.deletion_trigger) {
        query = query.eq('deletion_trigger', params.deletion_trigger);
      }

      if (params?.webhook_failed_only) {
        query = query.or('webhook_called.is.false,webhook_status.neq.200');
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching cleanup logs:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching cleanup logs:', error);
      return [];
    }
  }

  /**
   * Manually trigger cleanup for orphaned files
   */
  async cleanupOrphanedFiles(): Promise<{
    processed: number;
    successful: number;
    failed: number;
    errors: string[];
  }> {
    const result = {
      processed: 0,
      successful: 0,
      failed: 0,
      errors: [] as string[]
    };

    try {
      const client = supabaseAdmin || supabase;

      // Find soft-deleted files that haven't been cleaned up
      const { data: orphanedFiles, error } = await client
        .from('media_files')
        .select('id, filename, cloudinary_public_id, deleted_by, deleted_at')
        .not('deleted_at', 'is', null)
        .not('cloudinary_public_id', 'is', null)
        .not('cloudinary_public_id', 'eq', '');

      if (error) {
        throw new Error(`Failed to fetch orphaned files: ${error.message}`);
      }

      if (!orphanedFiles || orphanedFiles.length === 0) {
        console.log('✅ No orphaned files found');
        return result;
      }

      console.log(`🧹 Found ${orphanedFiles.length} potentially orphaned files`);

      for (const file of orphanedFiles) {
        result.processed++;

        try {
          // Check if we've already attempted cleanup for this file
          const { data: existingLog } = await client
            .from('media_deletion_log')
            .select('id')
            .eq('file_id', file.id)
            .eq('deletion_trigger', 'cleanup_orphaned')
            .single();

          if (existingLog) {
            console.log(`⏭️ Skipping file ${file.id} - already processed`);
            continue;
          }

          // Log the cleanup attempt
          await this.logDeletion({
            file_id: file.id,
            filename: file.filename,
            cloudinary_public_id: file.cloudinary_public_id,
            deleted_by: file.deleted_by,
            deletion_trigger: 'cleanup_orphaned'
          });

          // Attempt Cloudinary deletion
          const deleted = await deleteFromCloudinary(file.cloudinary_public_id);

          if (deleted) {
            result.successful++;
            await this.updateWebhookStatus({
              file_id: file.id,
              deletion_trigger: 'cleanup_orphaned',
              webhook_called: true,
              webhook_status: 200
            });
            console.log(`✅ Cleaned up file ${file.id}: ${file.filename}`);
          } else {
            result.failed++;
            const error = 'Cloudinary deletion returned false';
            result.errors.push(`File ${file.id}: ${error}`);
            await this.updateWebhookStatus({
              file_id: file.id,
              deletion_trigger: 'cleanup_orphaned',
              webhook_called: true,
              webhook_status: 500,
              webhook_error: error
            });
          }

        } catch (fileError) {
          result.failed++;
          const errorMsg = fileError instanceof Error ? fileError.message : 'Unknown error';
          result.errors.push(`File ${file.id}: ${errorMsg}`);
          
          await this.updateWebhookStatus({
            file_id: file.id,
            deletion_trigger: 'cleanup_orphaned',
            webhook_called: false,
            webhook_error: errorMsg
          });
        }
      }

      console.log('🧹 Cleanup completed:', result);
      return result;

    } catch (error) {
      console.error('❌ Cleanup orphaned files error:', error);
      result.errors.push(error instanceof Error ? error.message : 'Unknown error');
      return result;
    }
  }

  /**
   * Test the cleanup webhook
   */
  async testCleanupWebhook(cloudinary_public_id: string): Promise<boolean> {
    try {
      const response = await fetch('/api/webhooks/media-cleanup', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          cloudinary_public_id,
          file_id: 0,
          filename: 'test-file.jpg'
        }),
      });

      const result = await response.json();
      return result.success;
    } catch (error) {
      console.error('Error testing cleanup webhook:', error);
      return false;
    }
  }
}
