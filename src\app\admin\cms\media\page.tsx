'use client';

import React, { useState, useCallback } from 'react';
import {
  PhotoIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  CloudArrowUpIcon,
  TrashIcon,
  EyeIcon,
  DocumentDuplicateIcon,
  FolderIcon,
  VideoCameraIcon,
  DocumentIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';
import { useMediaLibrary } from '@/hooks/useMediaLibrary';
import type { MediaFile, MediaFolder } from '@/lib/services/mediaLibraryService';
import UploadModal from '@/components/admin/UploadModal';
import CreateFolderModal from '@/components/admin/CreateFolderModal';
import MediaFileModal from '@/components/admin/MediaFileModal';

export default function AdminMediaLibrary() {
  const {
    files: mediaFiles,
    folders,
    loading,
    error,
    pagination,
    uploadProgress,
    fetchFiles,
    uploadFiles,
    deleteFile,
    updateFile,
    createFolder,
    clearError,
  } = useMediaLibrary();

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFolder, setSelectedFolder] = useState<number | 'all'>('all');
  const [selectedType, setSelectedType] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedFiles, setSelectedFiles] = useState<number[]>([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showCreateFolderModal, setShowCreateFolderModal] = useState(false);
  const [selectedFile, setSelectedFile] = useState<MediaFile | null>(null);
  const [showFileModal, setShowFileModal] = useState(false);

  // Search and filter files
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    fetchFiles(
      {
        query: query || undefined,
        folder_id: selectedFolder === 'all' ? undefined : selectedFolder,
        file_type: selectedType === 'all' ? undefined : selectedType,
      },
      { page: 1 }
    );
  }, [selectedFolder, selectedType, fetchFiles]);

  const handleFolderFilter = useCallback((folderId: number | 'all') => {
    setSelectedFolder(folderId);
    fetchFiles(
      {
        query: searchQuery || undefined,
        folder_id: folderId === 'all' ? undefined : folderId,
        file_type: selectedType === 'all' ? undefined : selectedType,
      },
      { page: 1 }
    );
  }, [searchQuery, selectedType, fetchFiles]);

  const handleTypeFilter = useCallback((type: string) => {
    setSelectedType(type);
    fetchFiles(
      {
        query: searchQuery || undefined,
        folder_id: selectedFolder === 'all' ? undefined : selectedFolder,
        file_type: type === 'all' ? undefined : type,
      },
      { page: 1 }
    );
  }, [searchQuery, selectedFolder, fetchFiles]);

  const handleFileSelect = (fileId: number) => {
    setSelectedFiles(prev =>
      prev.includes(fileId)
        ? prev.filter(id => id !== fileId)
        : [...prev, fileId]
    );
  };

  const handleBulkDelete = async () => {
    if (selectedFiles.length === 0) return;

    if (confirm(`Are you sure you want to delete ${selectedFiles.length} file(s)?`)) {
      try {
        await Promise.all(
          selectedFiles.map(fileId => deleteFile(fileId, 'admin-user'))
        );
        setSelectedFiles([]);
      } catch (error) {
        console.error('Bulk delete error:', error);
      }
    }
  };

  const handleUpload = async (files: File[]) => {
    try {
      await uploadFiles(files, {
        folder: selectedFolder === 'all' ? undefined : folders.find(f => f.id === selectedFolder)?.name,
        uploaded_by: 'admin-user',
        visibility: 'public',
      });
      setShowUploadModal(false);
    } catch (error) {
      console.error('Upload error:', error);
    }
  };

  const handleCreateFolder = async (name: string, description?: string) => {
    try {
      await createFolder(name, undefined, description, 'admin-user');
      setShowCreateFolderModal(false);
    } catch (error) {
      console.error('Create folder error:', error);
    }
  };

  const handleFileClick = (file: MediaFile) => {
    setSelectedFile(file);
    setShowFileModal(true);
  };

  const handleFileAction = (file: MediaFile, action: 'view' | 'copy' | 'delete') => {
    switch (action) {
      case 'view':
        handleFileClick(file);
        break;
      case 'copy':
        navigator.clipboard.writeText(file.file_url);
        // You could add a toast notification here
        break;
      case 'delete':
        if (confirm(`Are you sure you want to delete "${file.filename}"?`)) {
          deleteFile(file.id, 'admin-user');
        }
        break;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return PhotoIcon;
    if (mimeType.startsWith('video/')) return VideoCameraIcon;
    return DocumentIcon;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {[...Array(12)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error loading media library</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <button
                  onClick={clearError}
                  className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6">
        <div className="mb-4 sm:mb-0">
          <h1 className="text-xl lg:text-2xl font-bold text-gray-900">Media Library</h1>
          <p className="text-sm lg:text-base text-gray-600">Manage your images, videos, and documents.</p>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowUploadModal(true)}
            className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 flex items-center text-sm"
          >
            <CloudArrowUpIcon className="h-4 w-4 mr-2" />
            Upload Files
          </button>
          <button
            onClick={() => setShowCreateFolderModal(true)}
            className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 flex items-center text-sm"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            New Folder
          </button>
          {selectedFiles.length > 0 && (
            <button
              onClick={handleBulkDelete}
              className="bg-red-500 text-white px-4 py-2 rounded-md hover:bg-red-600 flex items-center text-sm"
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              Delete ({selectedFiles.length})
            </button>
          )}
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <PhotoIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Total Files</p>
              <p className="text-lg font-semibold text-gray-900">{mediaFiles.length}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <FolderIcon className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Folders</p>
              <p className="text-lg font-semibold text-gray-900">{folders.length}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <VideoCameraIcon className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Videos</p>
              <p className="text-lg font-semibold text-gray-900">
                {mediaFiles.filter(f => f.mime_type.startsWith('video/')).length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <DocumentIcon className="h-6 w-6 text-orange-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Documents</p>
              <p className="text-lg font-semibold text-gray-900">
                {mediaFiles.filter(f => !f.mime_type.startsWith('image/') && !f.mime_type.startsWith('video/')).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow border mb-6">
        <div className="flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search files by name or tags..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
              />
            </div>
          </div>

          {/* Folder Filter */}
          <div className="flex items-center space-x-2">
            <FolderIcon className="h-5 w-5 text-gray-400" />
            <select
              value={selectedFolder}
              onChange={(e) => handleFolderFilter(e.target.value === 'all' ? 'all' : parseInt(e.target.value))}
              className="border border-gray-300 rounded-md px-3 py-2 focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="all">All Folders</option>
              {folders.map(folder => (
                <option key={folder.id} value={folder.id}>{folder.name}</option>
              ))}
            </select>
          </div>

          {/* Type Filter */}
          <div className="flex items-center space-x-2">
            <FunnelIcon className="h-5 w-5 text-gray-400" />
            <select
              value={selectedType}
              onChange={(e) => handleTypeFilter(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="all">All Types</option>
              <option value="image">Images</option>
              <option value="video">Videos</option>
              <option value="document">Documents</option>
            </select>
          </div>

          {/* View Mode */}
          <div className="flex items-center space-x-1 border border-gray-300 rounded-md">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 ${viewMode === 'grid' ? 'bg-orange-500 text-white' : 'text-gray-600 hover:bg-gray-100'}`}
            >
              <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm6 0a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1V4zM3 12a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1H4a1 1 0 01-1-1v-4zm6 0a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" clipRule="evenodd" />
              </svg>
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 ${viewMode === 'list' ? 'bg-orange-500 text-white' : 'text-gray-600 hover:bg-gray-100'}`}
            >
              <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 000 2h14a1 1 0 100-2H3zm0 4a1 1 0 000 2h14a1 1 0 100-2H3zm0 4a1 1 0 000 2h14a1 1 0 100-2H3z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Files Grid/List */}
      <div className="bg-white rounded-lg shadow border">
        {viewMode === 'grid' ? (
          <div className="p-6">
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {mediaFiles.map((file) => {
                const FileIcon = getFileIcon(file.mime_type);
                return (
                  <div
                    key={file.id}
                    className={`relative group cursor-pointer border-2 rounded-lg p-3 hover:border-orange-300 ${
                      selectedFiles.includes(file.id) ? 'border-orange-500 bg-orange-50' : 'border-gray-200'
                    }`}
                    onClick={() => handleFileSelect(file.id)}
                  >
                    {/* Checkbox */}
                    <div className="absolute top-2 left-2 z-10">
                      <input
                        type="checkbox"
                        checked={selectedFiles.includes(file.id)}
                        onChange={() => handleFileSelect(file.id)}
                        className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                      />
                    </div>

                    {/* File Preview */}
                    <div className="aspect-square mb-2 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                      {file.mime_type.startsWith('image/') && file.thumbnail_url ? (
                        <>
                          {/* eslint-disable-next-line @next/next/no-img-element */}
                          <img
                            src={file.thumbnail_url}
                            alt={file.alt_text || file.filename}
                            className="w-full h-full object-cover"
                          />
                        </>
                      ) : (
                        <FileIcon className="h-8 w-8 text-gray-400" />
                      )}
                    </div>

                    {/* File Info */}
                    <div className="text-center">
                      <p className="text-xs font-medium text-gray-900 truncate" title={file.filename}>
                        {file.filename}
                      </p>
                      <p className="text-xs text-gray-500">{formatFileSize(file.file_size)}</p>
                      {file.width && file.height && (
                        <p className="text-xs text-gray-400">
                          {file.width}×{file.height}
                        </p>
                      )}
                    </div>

                    {/* Usage Badge */}
                    {file.is_used && (
                      <div className="absolute top-2 right-2">
                        <span className="bg-green-100 text-green-800 text-xs px-1 py-0.5 rounded">
                          Used
                        </span>
                      </div>
                    )}

                    {/* Hover Actions */}
                    <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleFileAction(file, 'view');
                        }}
                        className="p-2 bg-white rounded-full hover:bg-gray-100"
                        title="View details"
                      >
                        <EyeIcon className="h-4 w-4 text-gray-600" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleFileAction(file, 'copy');
                        }}
                        className="p-2 bg-white rounded-full hover:bg-gray-100"
                        title="Copy URL"
                      >
                        <DocumentDuplicateIcon className="h-4 w-4 text-gray-600" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleFileAction(file, 'delete');
                        }}
                        className="p-2 bg-white rounded-full hover:bg-gray-100"
                        title="Delete file"
                      >
                        <TrashIcon className="h-4 w-4 text-red-600" />
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {mediaFiles.map((file) => {
              const FileIcon = getFileIcon(file.mime_type);
              return (
                <div
                  key={file.id}
                  className={`p-4 hover:bg-gray-50 ${
                    selectedFiles.includes(file.id) ? 'bg-orange-50' : ''
                  }`}
                >
                  <div className="flex items-center space-x-4">
                    <input
                      type="checkbox"
                      checked={selectedFiles.includes(file.id)}
                      onChange={() => handleFileSelect(file.id)}
                      className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                    />

                    <div className="flex-shrink-0">
                      {file.mime_type.startsWith('image/') && file.thumbnail_url ? (
                        <>
                          {/* eslint-disable-next-line @next/next/no-img-element */}
                          <img
                            src={file.thumbnail_url}
                            alt={file.alt_text || file.filename}
                            className="h-12 w-12 object-cover rounded-lg"
                          />
                        </>
                      ) : (
                        <div className="h-12 w-12 bg-gray-100 rounded-lg flex items-center justify-center">
                          <FileIcon className="h-6 w-6 text-gray-400" />
                        </div>
                      )}
                    </div>

                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">{file.filename}</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span>{formatFileSize(file.file_size)}</span>
                        {file.width && file.height && (
                          <span>{file.width}×{file.height}</span>
                        )}
                        <span>{file.folder_name || 'Root'}</span>
                        <span>Uploaded by {file.uploaded_by}</span>
                        <span>{formatDate(file.created_at)}</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {file.is_used && (
                        <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                          Used ({file.usage_count})
                        </span>
                      )}
                      <button
                        onClick={() => handleFileAction(file, 'view')}
                        className="p-1 text-gray-400 hover:text-gray-600"
                        title="View details"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleFileAction(file, 'copy')}
                        className="p-1 text-gray-400 hover:text-gray-600"
                        title="Copy URL"
                      >
                        <DocumentDuplicateIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleFileAction(file, 'delete')}
                        className="p-1 text-red-400 hover:text-red-600"
                        title="Delete file"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {mediaFiles.length === 0 && (
          <div className="p-12 text-center">
            <PhotoIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No files found</h3>
            <p className="text-gray-600 mb-4">Try adjusting your search or filter criteria, or upload your first file.</p>
            <button
              onClick={() => setShowUploadModal(true)}
              className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600"
            >
              Upload Your First File
            </button>
          </div>
        )}
      </div>

      {/* Upload Modal */}
      <UploadModal
        isOpen={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        onUpload={handleUpload}
        uploadProgress={uploadProgress}
      />

      {/* Create Folder Modal */}
      <CreateFolderModal
        isOpen={showCreateFolderModal}
        onClose={() => setShowCreateFolderModal(false)}
        onCreate={handleCreateFolder}
      />
    </div>
  );
}
