/**
 * Media Library Hook
 * Custom React hook for media library operations
 */

import { useState, useEffect, useCallback } from 'react';
import type { MediaFile, MediaFolder, MediaSearchFilters, MediaSearchOptions } from '@/lib/services/mediaLibraryService';

interface MediaLibraryState {
  files: MediaFile[];
  folders: MediaFolder[];
  loading: boolean;
  error: string | null;
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

interface UploadProgress {
  file: File;
  progress: number;
  status: 'uploading' | 'success' | 'error';
  error?: string;
  result?: MediaFile;
}

export function useMediaLibrary() {
  const [state, setState] = useState<MediaLibraryState>({
    files: [],
    folders: [],
    loading: false,
    error: null,
    pagination: {
      total: 0,
      page: 1,
      limit: 20,
      pages: 0,
    },
  });

  const [uploadProgress, setUploadProgress] = useState<UploadProgress[]>([]);

  /**
   * Fetch media files with filters and pagination
   */
  const fetchFiles = useCallback(async (
    filters: MediaSearchFilters = {},
    options: MediaSearchOptions = {}
  ) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const searchParams = new URLSearchParams();
      
      // Add filters to search params
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            searchParams.set(key, value.join(','));
          } else {
            searchParams.set(key, value.toString());
          }
        }
      });

      // Add options to search params
      Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.set(key, value.toString());
        }
      });

      const response = await fetch(`/api/media/files?${searchParams.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch media files');
      }

      const data = await response.json();
      
      setState(prev => ({
        ...prev,
        files: data.data,
        pagination: data.pagination,
        loading: false,
      }));

    } catch (error) {
      console.error('Error fetching media files:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to fetch media files',
        loading: false,
      }));
    }
  }, []);

  /**
   * Fetch folders
   */
  const fetchFolders = useCallback(async () => {
    try {
      const response = await fetch('/api/media/folders');
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch folders');
      }

      const data = await response.json();
      
      setState(prev => ({
        ...prev,
        folders: data.data,
      }));

    } catch (error) {
      console.error('Error fetching folders:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to fetch folders',
      }));
    }
  }, []);

  /**
   * Upload files
   */
  const uploadFiles = useCallback(async (
    files: File[],
    options: {
      folder?: string;
      tags?: string[];
      alt_text?: string;
      caption?: string;
      description?: string;
      title?: string;
      visibility?: 'public' | 'private' | 'restricted';
      uploaded_by: string;
    }
  ) => {
    const uploads: UploadProgress[] = files.map(file => ({
      file,
      progress: 0,
      status: 'uploading' as const,
    }));

    setUploadProgress(uploads);

    const results = await Promise.allSettled(
      files.map(async (file, index) => {
        try {
          const formData = new FormData();
          formData.append('file', file);
          
          // Add metadata
          Object.entries(options).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
              if (Array.isArray(value)) {
                formData.append(key, value.join(','));
              } else {
                formData.append(key, value.toString());
              }
            }
          });

          const response = await fetch('/api/media/upload', {
            method: 'POST',
            body: formData,
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Upload failed');
          }

          const data = await response.json();
          
          // Update upload progress
          setUploadProgress(prev => prev.map((upload, i) => 
            i === index 
              ? { ...upload, progress: 100, status: 'success', result: data.data }
              : upload
          ));

          return data.data;

        } catch (error) {
          console.error('Upload error:', error);
          
          // Update upload progress with error
          setUploadProgress(prev => prev.map((upload, i) => 
            i === index 
              ? { 
                  ...upload, 
                  progress: 0, 
                  status: 'error', 
                  error: error instanceof Error ? error.message : 'Upload failed' 
                }
              : upload
          ));

          throw error;
        }
      })
    );

    // Clear upload progress after a delay
    setTimeout(() => {
      setUploadProgress([]);
    }, 3000);

    return results;
  }, []);

  /**
   * Delete media file (soft delete by default)
   */
  const deleteFile = useCallback(async (id: number, userId: string, hardDelete: boolean = false) => {
    try {
      const url = new URL(`/api/media/files/${id}`, window.location.origin);
      url.searchParams.set('user_id', userId);
      if (hardDelete) {
        url.searchParams.set('hard_delete', 'true');
      }

      const response = await fetch(url.toString(), {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Delete failed');
      }

      const result = await response.json();
      console.log(`✅ File ${hardDelete ? 'permanently' : 'soft'} deleted:`, result);

      // Remove from local state
      setState(prev => ({
        ...prev,
        files: prev.files.filter(file => file.id !== id),
      }));

      return true;

    } catch (error) {
      console.error('Delete error:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Delete failed',
      }));
      return false;
    }
  }, []);

  /**
   * Update media file metadata
   */
  const updateFile = useCallback(async (
    id: number,
    updates: {
      alt_text?: string;
      caption?: string;
      description?: string;
      title?: string;
      visibility?: 'public' | 'private' | 'restricted';
    }
  ) => {
    try {
      const response = await fetch(`/api/media/files/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Update failed');
      }

      const data = await response.json();

      // Update local state
      setState(prev => ({
        ...prev,
        files: prev.files.map(file => 
          file.id === id ? data.data : file
        ),
      }));

      return data.data;

    } catch (error) {
      console.error('Update error:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Update failed',
      }));
      throw error;
    }
  }, []);

  /**
   * Create folder
   */
  const createFolder = useCallback(async (
    name: string,
    parentId?: number,
    description?: string,
    createdBy?: string
  ) => {
    try {
      const response = await fetch('/api/media/folders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          parent_id: parentId,
          description,
          created_by: createdBy || 'system',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create folder');
      }

      const data = await response.json();

      // Add to local state
      setState(prev => ({
        ...prev,
        folders: [...prev.folders, data.data],
      }));

      return data.data;

    } catch (error) {
      console.error('Create folder error:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to create folder',
      }));
      throw error;
    }
  }, []);

  /**
   * Clear error
   */
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Load initial data
  useEffect(() => {
    fetchFiles();
    fetchFolders();
  }, [fetchFiles, fetchFolders]);

  return {
    // State
    files: state.files,
    folders: state.folders,
    loading: state.loading,
    error: state.error,
    pagination: state.pagination,
    uploadProgress,

    // Actions
    fetchFiles,
    fetchFolders,
    uploadFiles,
    deleteFile,
    updateFile,
    createFolder,
    clearError,
  };
}
