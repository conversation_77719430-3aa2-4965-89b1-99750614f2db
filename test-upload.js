/**
 * Simple test script to verify upload functionality
 * Run with: node test-upload.js
 */

const fs = require('fs');
const path = require('path');

// Create a simple test image file (1x1 pixel PNG)
const testImageBuffer = Buffer.from([
  0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
  0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
  0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
  0x01, 0x00, 0x01, 0x5C, 0xC2, 0x5D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
  0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
]);

async function testUpload() {
  try {
    console.log('🧪 Testing media upload functionality...');
    
    // Create FormData
    const FormData = require('form-data');
    const formData = new FormData();
    
    // Add the test image
    formData.append('file', testImageBuffer, {
      filename: 'test-image.png',
      contentType: 'image/png'
    });
    
    // Add metadata
    formData.append('uploaded_by', 'test-user');
    formData.append('visibility', 'public');
    formData.append('title', 'Test Upload Image');
    formData.append('alt_text', 'A test image for upload verification');
    
    // Make the request
    const response = await fetch('http://localhost:3001/api/media/upload', {
      method: 'POST',
      body: formData,
      headers: formData.getHeaders()
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Upload successful!');
      console.log('📄 Response:', JSON.stringify(result, null, 2));
      
      // Test fetching the files to see if it appears
      console.log('\n🔍 Fetching media files to verify...');
      const filesResponse = await fetch('http://localhost:3001/api/media/files');
      const filesResult = await filesResponse.json();
      
      if (filesResponse.ok) {
        console.log('✅ Files fetched successfully!');
        console.log(`📊 Total files: ${filesResult.data.length}`);
        
        if (filesResult.data.length > 0) {
          console.log('📁 Latest file:', {
            id: filesResult.data[0].id,
            filename: filesResult.data[0].filename,
            file_url: filesResult.data[0].file_url,
            created_at: filesResult.data[0].created_at
          });
        }
      } else {
        console.log('❌ Failed to fetch files:', filesResult);
      }
      
    } else {
      console.log('❌ Upload failed!');
      console.log('📄 Error response:', JSON.stringify(result, null, 2));
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testUpload();
