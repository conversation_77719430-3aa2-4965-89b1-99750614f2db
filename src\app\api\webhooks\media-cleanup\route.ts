import { NextRequest, NextResponse } from 'next/server';
import { deleteFromCloudinary } from '@/lib/cloudinary';

interface MediaCleanupPayload {
  event_type: 'media_file_deleted' | 'media_file_cleanup';
  file_id: number;
  filename: string;
  cloudinary_public_id: string;
  deleted_by?: string;
  deleted_at?: string;
  trigger_source: 'database_delete' | 'cleanup_orphaned' | 'manual';
}

/**
 * Webhook endpoint for handling media cleanup when database records are deleted
 * This leverages our existing Cloudinary deletion functionality
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🧹 Media cleanup webhook triggered');

    // Parse the payload
    const payload: MediaCleanupPayload = await request.json();
    
    console.log('📋 Cleanup payload:', {
      event_type: payload.event_type,
      file_id: payload.file_id,
      filename: payload.filename,
      cloudinary_public_id: payload.cloudinary_public_id,
      trigger_source: payload.trigger_source
    });

    // Validate required fields
    if (!payload.cloudinary_public_id) {
      console.warn('⚠️ No Cloudinary public ID provided, skipping cleanup');
      return NextResponse.json({
        success: true,
        message: 'No Cloudinary public ID provided, cleanup skipped',
        skipped: true
      });
    }

    // Perform Cloudinary cleanup using our existing function
    console.log('🌩️ Attempting to delete from Cloudinary:', payload.cloudinary_public_id);
    
    try {
      const cloudinaryDeleted = await deleteFromCloudinary(payload.cloudinary_public_id);
      
      if (cloudinaryDeleted) {
        console.log('✅ Successfully deleted from Cloudinary:', {
          file_id: payload.file_id,
          filename: payload.filename,
          cloudinary_public_id: payload.cloudinary_public_id
        });

        return NextResponse.json({
          success: true,
          message: 'Media file successfully deleted from Cloudinary',
          file_id: payload.file_id,
          filename: payload.filename,
          cloudinary_public_id: payload.cloudinary_public_id,
          trigger_source: payload.trigger_source
        });
      } else {
        console.warn('⚠️ Cloudinary deletion returned false:', {
          file_id: payload.file_id,
          cloudinary_public_id: payload.cloudinary_public_id
        });

        return NextResponse.json({
          success: false,
          message: 'Failed to delete from Cloudinary',
          file_id: payload.file_id,
          cloudinary_public_id: payload.cloudinary_public_id,
          error: 'Cloudinary deletion returned false'
        }, { status: 500 });
      }
    } catch (cloudinaryError) {
      console.error('❌ Cloudinary deletion error:', cloudinaryError);
      
      return NextResponse.json({
        success: false,
        message: 'Error deleting from Cloudinary',
        file_id: payload.file_id,
        cloudinary_public_id: payload.cloudinary_public_id,
        error: cloudinaryError instanceof Error ? cloudinaryError.message : 'Unknown Cloudinary error'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('❌ Media cleanup webhook error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Media cleanup webhook failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * GET endpoint for testing the webhook
 */
export async function GET() {
  return NextResponse.json({
    message: 'Media cleanup webhook endpoint is active',
    timestamp: new Date().toISOString(),
    endpoints: {
      POST: 'Handles media cleanup requests from database triggers',
      GET: 'Health check endpoint'
    }
  });
}

/**
 * Manual cleanup endpoint for testing
 */
export async function PUT(request: NextRequest) {
  try {
    const { cloudinary_public_id, file_id, filename } = await request.json();
    
    if (!cloudinary_public_id) {
      return NextResponse.json({
        success: false,
        message: 'cloudinary_public_id is required'
      }, { status: 400 });
    }

    console.log('🧪 Manual cleanup test for:', cloudinary_public_id);
    
    const deleted = await deleteFromCloudinary(cloudinary_public_id);
    
    return NextResponse.json({
      success: deleted,
      message: deleted ? 'Successfully deleted from Cloudinary' : 'Failed to delete from Cloudinary',
      file_id,
      filename,
      cloudinary_public_id,
      trigger_source: 'manual_test'
    });

  } catch (error) {
    console.error('❌ Manual cleanup test error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Manual cleanup test failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
