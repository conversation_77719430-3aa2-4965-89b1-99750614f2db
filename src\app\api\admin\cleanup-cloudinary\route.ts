import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { deleteFromCloudinary } from '@/lib/cloudinary';

interface OrphanedFile {
  id: number;
  filename: string;
  cloudinary_public_id: string;
  deleted_at: string | null;
  deleted_by: string | null;
}

/**
 * Find orphaned files that exist in database but should be cleaned from Cloudinary
 */
async function findOrphanedFiles(): Promise<OrphanedFile[]> {
  if (!supabaseAdmin) {
    throw new Error('Supabase admin client not available');
  }

  // Find files that are soft deleted but might still exist in Cloudinary
  const { data: softDeleted, error } = await supabaseAdmin
    .from('media_files')
    .select('id, filename, cloudinary_public_id, deleted_at, deleted_by')
    .not('deleted_at', 'is', null)
    .not('cloudinary_public_id', 'is', null)
    .not('cloudinary_public_id', 'eq', '');

  if (error) {
    throw new Error(`Failed to find orphaned files: ${error.message}`);
  }

  return softDeleted || [];
}

/**
 * GET - List orphaned files (dry run)
 */
export async function GET() {
  try {
    console.log('🔍 Finding orphaned Cloudinary files...');
    
    const orphanedFiles = await findOrphanedFiles();
    
    return NextResponse.json({
      success: true,
      message: `Found ${orphanedFiles.length} orphaned files`,
      count: orphanedFiles.length,
      files: orphanedFiles.map(file => ({
        id: file.id,
        filename: file.filename,
        cloudinary_public_id: file.cloudinary_public_id,
        deleted_at: file.deleted_at,
        deleted_by: file.deleted_by
      }))
    });

  } catch (error) {
    console.error('❌ Error finding orphaned files:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Failed to find orphaned files',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * POST - Actually cleanup orphaned files
 */
export async function POST(request: NextRequest) {
  try {
    const { confirm } = await request.json();
    
    if (confirm !== 'DELETE_ORPHANED_FILES') {
      return NextResponse.json({
        success: false,
        message: 'Confirmation required. Send {"confirm": "DELETE_ORPHANED_FILES"} to proceed.'
      }, { status: 400 });
    }

    console.log('🧹 Starting Cloudinary cleanup...');
    
    const orphanedFiles = await findOrphanedFiles();
    
    if (orphanedFiles.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No orphaned files found',
        processed: 0,
        successful: 0,
        failed: 0
      });
    }

    console.log(`📋 Found ${orphanedFiles.length} orphaned files to cleanup`);
    
    let successful = 0;
    let failed = 0;
    const errors: string[] = [];
    
    for (const file of orphanedFiles) {
      try {
        console.log(`🧹 Cleaning up: ${file.filename} (${file.cloudinary_public_id})`);
        
        const deleted = await deleteFromCloudinary(file.cloudinary_public_id);
        
        if (deleted) {
          successful++;
          console.log(`✅ Successfully deleted: ${file.filename}`);
        } else {
          failed++;
          const error = `Failed to delete ${file.filename} (${file.cloudinary_public_id})`;
          errors.push(error);
          console.warn(`⚠️ ${error}`);
        }
        
        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (fileError) {
        failed++;
        const error = `Error deleting ${file.filename}: ${fileError instanceof Error ? fileError.message : 'Unknown error'}`;
        errors.push(error);
        console.error(`❌ ${error}`);
      }
    }
    
    const result = {
      success: true,
      message: `Cleanup completed: ${successful} successful, ${failed} failed`,
      processed: orphanedFiles.length,
      successful,
      failed,
      errors: errors.length > 0 ? errors : undefined
    };
    
    console.log('📊 Cleanup completed:', result);
    
    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Cleanup failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * DELETE - Cleanup a specific file by public ID
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const publicId = searchParams.get('public_id');
    
    if (!publicId) {
      return NextResponse.json({
        success: false,
        message: 'public_id query parameter is required'
      }, { status: 400 });
    }

    console.log('🗑️ Deleting specific file from Cloudinary:', publicId);
    
    const deleted = await deleteFromCloudinary(publicId);
    
    return NextResponse.json({
      success: deleted,
      message: deleted ? 'Successfully deleted from Cloudinary' : 'Failed to delete from Cloudinary',
      cloudinary_public_id: publicId
    });

  } catch (error) {
    console.error('❌ Delete failed:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Delete failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
