<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Upload</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
        }
        .upload-area.dragover {
            border-color: #007cba;
            background-color: #f0f8ff;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #005a87;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>🧪 Media Upload Test</h1>
    <p>Test the media upload functionality directly in the browser.</p>
    
    <div class="upload-area" id="uploadArea">
        <p>📁 Drag and drop files here or click to select</p>
        <input type="file" id="fileInput" multiple accept="image/*,video/*,.pdf,.doc,.docx" style="display: none;">
        <button onclick="document.getElementById('fileInput').click()">Select Files</button>
    </div>
    
    <div id="result"></div>
    
    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const result = document.getElementById('result');
        
        // Handle file input change
        fileInput.addEventListener('change', handleFiles);
        
        // Handle drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            uploadFiles(files);
        });
        
        function handleFiles(e) {
            const files = Array.from(e.target.files);
            uploadFiles(files);
        }
        
        async function uploadFiles(files) {
            if (files.length === 0) return;
            
            result.innerHTML = '<p>⏳ Uploading files...</p>';
            
            try {
                for (const file of files) {
                    await uploadSingleFile(file);
                }
                
                // Refresh the media library to show new files
                await checkMediaLibrary();
                
            } catch (error) {
                console.error('Upload error:', error);
                result.innerHTML = `<div class="result error">❌ Upload failed: ${error.message}</div>`;
            }
        }
        
        async function uploadSingleFile(file) {
            console.log('📤 Uploading file:', file.name);
            
            const formData = new FormData();
            formData.append('file', file);
            formData.append('uploaded_by', 'test-user');
            formData.append('visibility', 'public');
            formData.append('title', `Test upload: ${file.name}`);
            formData.append('alt_text', `Test image: ${file.name}`);
            
            const response = await fetch('http://localhost:3001/api/media/upload', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || 'Upload failed');
            }
            
            console.log('✅ Upload successful:', data);
            result.innerHTML = `<div class="result success">✅ Upload successful! File: ${file.name}</div>`;
            
            return data;
        }
        
        async function checkMediaLibrary() {
            try {
                const response = await fetch('http://localhost:3001/api/media/files');
                const data = await response.json();
                
                if (response.ok) {
                    const fileCount = data.data.length;
                    result.innerHTML += `<div class="result success">📊 Media library now has ${fileCount} files</div>`;
                    
                    if (fileCount > 0) {
                        const latestFile = data.data[0];
                        result.innerHTML += `<div class="result success">📁 Latest file: ${latestFile.filename}</div>`;
                    }
                } else {
                    console.error('Failed to fetch media files:', data);
                }
            } catch (error) {
                console.error('Error checking media library:', error);
            }
        }
        
        // Load initial file count
        checkMediaLibrary();
    </script>
</body>
</html>
