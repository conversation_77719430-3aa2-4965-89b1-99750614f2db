'use client';

import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { MediaFile } from '@/types/media';

interface ImageViewModalProps {
  file: MediaFile | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function ImageViewModal({ file, isOpen, onClose }: ImageViewModalProps) {
  if (!file) return null;

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-75" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-6xl transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b">
                  <div>
                    <Dialog.Title as="h3" className="text-lg font-medium text-gray-900">
                      {file.filename}
                    </Dialog.Title>
                    <p className="text-sm text-gray-500">
                      {file.width} × {file.height} pixels
                    </p>
                  </div>
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500"
                    onClick={onClose}
                  >
                    <span className="sr-only">Close</span>
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>

                {/* Image */}
                <div className="p-4">
                  <div className="flex justify-center">
                    <img
                      src={file.file_url}
                      alt={file.alt_text || file.filename}
                      className="max-w-full max-h-[70vh] object-contain rounded-lg"
                    />
                  </div>
                </div>

                {/* Footer with image details */}
                <div className="px-4 py-3 bg-gray-50 border-t">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-gray-700">File Size:</span>
                      <p className="text-gray-600">{formatFileSize(file.file_size)}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Format:</span>
                      <p className="text-gray-600">{file.file_extension.toUpperCase()}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Uploaded:</span>
                      <p className="text-gray-600">{formatDate(file.created_at)}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Uploaded by:</span>
                      <p className="text-gray-600">{file.uploaded_by}</p>
                    </div>
                  </div>
                  
                  {file.alt_text && (
                    <div className="mt-3">
                      <span className="font-medium text-gray-700">Alt Text:</span>
                      <p className="text-gray-600">{file.alt_text}</p>
                    </div>
                  )}
                  
                  {file.caption && (
                    <div className="mt-2">
                      <span className="font-medium text-gray-700">Caption:</span>
                      <p className="text-gray-600">{file.caption}</p>
                    </div>
                  )}
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}

// Helper functions
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}
