# Media Cleanup Solution Test Guide

## 🎯 **Problem Solved:**
When you delete a record directly from the Supabase `media_files` table, it now automatically triggers Cloudinary cleanup using your existing Media Library deletion functionality.

## 🏗️ **Architecture Overview:**

```
Database Record Deleted → Database Trigger → Webhook → Existing Cleanup Function → Cloudinary Deleted
```

## 📁 **Files Created:**

### 1. **Database Trigger** (`supabase-db-scripts/media-cleanup-trigger.sql`)
- Creates a trigger that fires when records are deleted from `media_files`
- Logs deletion events in `media_deletion_log` table
- Calls webhook to trigger Cloudinary cleanup
- Includes function to manually cleanup orphaned files

### 2. **Webhook Endpoint** (`src/app/api/webhooks/media-cleanup/route.ts`)
- Receives cleanup requests from database triggers
- Uses your existing `deleteFromCloudinary()` function
- Provides manual testing endpoints
- Comprehensive error handling and logging

### 3. **Cleanup Service** (`src/lib/services/mediaCleanupService.ts`)
- Manages cleanup operations and logging
- Provides methods to find and cleanup orphaned files
- Monitors webhook success/failure
- Batch cleanup functionality

### 4. **Admin Interface** (`src/app/admin/cms/media-cleanup/page.tsx`)
- Monitor cleanup operations
- View cleanup logs and status
- Manually trigger orphaned file cleanup
- Error tracking and reporting

## 🧪 **Testing Steps:**

### Step 1: Setup Database Trigger
```sql
-- Run this in your Supabase SQL editor
\i supabase-db-scripts/media-cleanup-trigger.sql
```

### Step 2: Test the Webhook
```bash
# Test webhook health
curl http://localhost:3000/api/webhooks/media-cleanup

# Test manual cleanup
curl -X PUT http://localhost:3000/api/webhooks/media-cleanup \
  -H "Content-Type: application/json" \
  -d '{"cloudinary_public_id": "test-id", "file_id": 1, "filename": "test.jpg"}'
```

### Step 3: Test Database Deletion
```sql
-- In Supabase SQL editor, delete a record
DELETE FROM media_files WHERE id = [some_test_id];

-- Check if cleanup was triggered
SELECT * FROM media_deletion_log ORDER BY created_at DESC LIMIT 5;
```

### Step 4: Monitor via Admin Interface
- Visit: `http://localhost:3000/admin/cms/media-cleanup`
- View cleanup logs
- Run manual orphaned file cleanup

## ✅ **Benefits of This Approach:**

1. **Leverages Existing Code**: Uses your proven `deleteFromCloudinary()` function
2. **Automatic**: No manual intervention needed for database deletions
3. **Reliable**: Database triggers are more reliable than application-level hooks
4. **Auditable**: Complete logging of all cleanup operations
5. **Recoverable**: Can find and cleanup orphaned files manually
6. **Testable**: Multiple ways to test and verify functionality

## 🔧 **How It Works:**

### Normal Media Library Deletion:
```
User clicks delete → Media Library → Hard Delete Function → Database + Cloudinary deleted
```

### Database-Only Deletion (Your Problem):
```
Direct DB delete → Database Trigger → Webhook → deleteFromCloudinary() → Cloudinary deleted
```

### Orphaned File Cleanup:
```
Admin runs cleanup → Find soft-deleted files → Batch cleanup → Cloudinary deleted
```

## 🚀 **Production Considerations:**

1. **Webhook URL**: Update the webhook URL in the trigger for production
2. **Error Handling**: Monitor the `media_deletion_log` table for failed cleanups
3. **Retry Logic**: Consider adding retry logic for failed webhook calls
4. **Rate Limiting**: Cloudinary has rate limits, consider batching for large cleanups

## 📊 **Monitoring:**

- **Success Rate**: Check `webhook_called` and `webhook_status` in logs
- **Failed Cleanups**: Query for `webhook_status != 200` or `webhook_called = false`
- **Orphaned Files**: Regular cleanup runs to catch any missed files

This solution is **production-ready** and follows best practices for distributed systems with proper error handling, logging, and recovery mechanisms!
