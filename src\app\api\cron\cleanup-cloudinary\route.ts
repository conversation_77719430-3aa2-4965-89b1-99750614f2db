import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { deleteFromCloudinary } from '@/lib/cloudinary';

/**
 * <PERSON>ron job to automatically cleanup orphaned Cloudinary files
 * This runs periodically to detect files deleted from database and clean them from Cloudinary
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🧹 Starting automatic Cloudinary cleanup...');

    if (!supabaseAdmin) {
      throw new Error('Supabase admin client not available');
    }

    // Find files that are soft deleted but might still exist in Cloudinary
    const { data: orphanedFiles, error } = await supabaseAdmin
      .from('media_files')
      .select('id, filename, cloudinary_public_id, deleted_at, deleted_by')
      .not('deleted_at', 'is', null)
      .not('cloudinary_public_id', 'is', null)
      .not('cloudinary_public_id', 'eq', '');

    if (error) {
      throw new Error(`Failed to find orphaned files: ${error.message}`);
    }

    if (!orphanedFiles || orphanedFiles.length === 0) {
      console.log('✅ No orphaned files found');
      return NextResponse.json({
        success: true,
        message: 'No orphaned files found',
        processed: 0,
        successful: 0,
        failed: 0
      });
    }

    console.log(`📋 Found ${orphanedFiles.length} orphaned files to cleanup`);

    let successful = 0;
    let failed = 0;
    const errors: string[] = [];

    // Process each orphaned file
    for (const file of orphanedFiles) {
      try {
        console.log(`🧹 Cleaning up: ${file.filename} (${file.cloudinary_public_id})`);
        
        // Delete from Cloudinary using existing function
        const deleted = await deleteFromCloudinary(file.cloudinary_public_id);
        
        if (deleted) {
          successful++;
          console.log(`✅ Successfully deleted from Cloudinary: ${file.filename}`);
          
          // Now permanently delete from database since Cloudinary is cleaned
          const { error: dbError } = await supabaseAdmin
            .from('media_files')
            .delete()
            .eq('id', file.id);
            
          if (dbError) {
            console.warn(`⚠️ Failed to remove database record for ${file.filename}:`, dbError);
          } else {
            console.log(`🗑️ Permanently removed database record for: ${file.filename}`);
          }
        } else {
          failed++;
          const error = `Failed to delete ${file.filename} from Cloudinary`;
          errors.push(error);
          console.warn(`⚠️ ${error}`);
        }
        
        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (fileError) {
        failed++;
        const error = `Error processing ${file.filename}: ${fileError instanceof Error ? fileError.message : 'Unknown error'}`;
        errors.push(error);
        console.error(`❌ ${error}`);
      }
    }

    const result = {
      success: true,
      message: `Cleanup completed: ${successful} successful, ${failed} failed`,
      processed: orphanedFiles.length,
      successful,
      failed,
      errors: errors.length > 0 ? errors : undefined
    };

    console.log('📊 Automatic cleanup completed:', result);

    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ Automatic cleanup failed:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Automatic cleanup failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Manual trigger endpoint
 */
export async function POST() {
  return GET({} as NextRequest);
}
