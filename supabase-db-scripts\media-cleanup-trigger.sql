-- ========================================
-- MEDIA CLEANUP TRIGGER
-- Automatically triggers Cloudinary cleanup when database records are deleted
-- ========================================

-- Function to handle media file deletion cleanup
CREATE OR REPLACE FUNCTION handle_media_file_deletion()
RETURNS TRIGGER AS $$
DECLARE
    webhook_url TEXT;
    payload JSONB;
    response_status INTEGER;
BEGIN
    -- Only proceed if the deleted record has a Cloudinary public ID
    IF OLD.cloudinary_public_id IS NOT NULL AND OLD.cloudinary_public_id != '' THEN
        
        -- Log the deletion event
        INSERT INTO media_deletion_log (
            file_id,
            filename,
            cloudinary_public_id,
            deleted_by,
            deletion_trigger,
            created_at
        ) VALUES (
            OLD.id,
            OLD.filename,
            OLD.cloudinary_public_id,
            OLD.deleted_by,
            'database_delete',
            NOW()
        );

        -- Prepare webhook payload
        payload := jsonb_build_object(
            'event_type', 'media_file_deleted',
            'file_id', OLD.id,
            'filename', OLD.filename,
            'cloudinary_public_id', OLD.cloudinary_public_id,
            'deleted_by', OLD.deleted_by,
            'deleted_at', OLD.deleted_at,
            'trigger_source', 'database_delete'
        );

        -- Get the webhook URL from environment or use default
        -- In production, this should be your actual domain
        webhook_url := COALESCE(
            current_setting('app.webhook_base_url', true),
            'http://localhost:3000'
        ) || '/api/webhooks/media-cleanup';

        -- Call the webhook using pg_net extension (if available)
        -- Note: This requires the pg_net extension to be enabled in Supabase
        BEGIN
            SELECT status INTO response_status
            FROM net.http_post(
                url := webhook_url,
                headers := '{"Content-Type": "application/json"}'::jsonb,
                body := payload::text
            );
            
            -- Log successful webhook call
            UPDATE media_deletion_log 
            SET 
                webhook_called = true,
                webhook_status = response_status,
                updated_at = NOW()
            WHERE file_id = OLD.id AND deletion_trigger = 'database_delete';
            
        EXCEPTION WHEN OTHERS THEN
            -- Log failed webhook call
            UPDATE media_deletion_log 
            SET 
                webhook_called = false,
                webhook_error = SQLERRM,
                updated_at = NOW()
            WHERE file_id = OLD.id AND deletion_trigger = 'database_delete';
        END;
    END IF;

    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Create deletion log table for tracking
CREATE TABLE IF NOT EXISTS media_deletion_log (
    id SERIAL PRIMARY KEY,
    file_id INTEGER NOT NULL,
    filename VARCHAR(500) NOT NULL,
    cloudinary_public_id VARCHAR(500) NOT NULL,
    deleted_by VARCHAR(255),
    deletion_trigger VARCHAR(50) NOT NULL, -- 'media_library', 'database_delete', 'manual'
    webhook_called BOOLEAN DEFAULT false,
    webhook_status INTEGER,
    webhook_error TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for the log table
CREATE INDEX IF NOT EXISTS idx_media_deletion_log_file_id ON media_deletion_log(file_id);
CREATE INDEX IF NOT EXISTS idx_media_deletion_log_trigger ON media_deletion_log(deletion_trigger);
CREATE INDEX IF NOT EXISTS idx_media_deletion_log_created_at ON media_deletion_log(created_at);

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS trigger_media_file_deletion_cleanup ON media_files;

-- Create the trigger for DELETE operations
CREATE TRIGGER trigger_media_file_deletion_cleanup
    AFTER DELETE ON media_files
    FOR EACH ROW
    EXECUTE FUNCTION handle_media_file_deletion();

-- Alternative approach: Function to manually trigger cleanup for existing deleted records
CREATE OR REPLACE FUNCTION cleanup_orphaned_cloudinary_files()
RETURNS TABLE(
    file_id INTEGER,
    filename VARCHAR(500),
    cloudinary_public_id VARCHAR(500),
    cleanup_status TEXT
) AS $$
DECLARE
    webhook_url TEXT;
    payload JSONB;
    rec RECORD;
BEGIN
    webhook_url := COALESCE(
        current_setting('app.webhook_base_url', true),
        'http://localhost:3000'
    ) || '/api/webhooks/media-cleanup';

    -- Find records that were soft deleted but might still have Cloudinary files
    FOR rec IN 
        SELECT id, filename, cloudinary_public_id, deleted_by, deleted_at
        FROM media_files 
        WHERE deleted_at IS NOT NULL 
        AND cloudinary_public_id IS NOT NULL
        AND id NOT IN (
            SELECT file_id FROM media_deletion_log 
            WHERE deletion_trigger = 'cleanup_orphaned'
        )
    LOOP
        -- Log the cleanup attempt
        INSERT INTO media_deletion_log (
            file_id,
            filename,
            cloudinary_public_id,
            deleted_by,
            deletion_trigger,
            created_at
        ) VALUES (
            rec.id,
            rec.filename,
            rec.cloudinary_public_id,
            rec.deleted_by,
            'cleanup_orphaned',
            NOW()
        );

        -- Prepare webhook payload
        payload := jsonb_build_object(
            'event_type', 'media_file_cleanup',
            'file_id', rec.id,
            'filename', rec.filename,
            'cloudinary_public_id', rec.cloudinary_public_id,
            'deleted_by', rec.deleted_by,
            'deleted_at', rec.deleted_at,
            'trigger_source', 'cleanup_orphaned'
        );

        -- Try to call the webhook
        BEGIN
            PERFORM net.http_post(
                url := webhook_url,
                headers := '{"Content-Type": "application/json"}'::jsonb,
                body := payload::text
            );
            
            file_id := rec.id;
            filename := rec.filename;
            cloudinary_public_id := rec.cloudinary_public_id;
            cleanup_status := 'webhook_called';
            RETURN NEXT;
            
        EXCEPTION WHEN OTHERS THEN
            file_id := rec.id;
            filename := rec.filename;
            cloudinary_public_id := rec.cloudinary_public_id;
            cleanup_status := 'webhook_failed: ' || SQLERRM;
            RETURN NEXT;
        END;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA net TO postgres;
GRANT EXECUTE ON FUNCTION net.http_post TO postgres;
